import numpy as np
import pandas as pd
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, mean_squared_error, mean_absolute_error, r2_score
from tensorflow.keras.utils import to_categorical
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import Conv2D, MaxPooling2D, Flatten, Dense, Dropout
import matplotlib.pyplot as plt

def build_model(input_shape):
    model = Sequential()
    model.add(Conv2D(32, (3,3), activation='relu', padding='same', input_shape=input_shape))
    model.add(MaxPooling2D(pool_size=(2,2)))
    model.add(Conv2D(64, (3,3), activation='relu', padding='same'))
    model.add(MaxPooling2D(pool_size=(2,2)))
    model.add(Dropout(0.2))
    model.add(Flatten())
    model.add(Dense(100, activation='relu'))
    model.add(Dense(5, activation='softmax'))  # 5 classes
    model.compile(loss='categorical_crossentropy', optimizer='adam', metrics=['accuracy'])
    return model

def process():
    # Load dataset
    df = pd.read_csv("malariadataset.csv")
    label_map = {
        "Normal": 0,
        "Plasmodium Falciparum": 1,
        "Plasmodium Malariae": 2,
        "Plasmodium Ovale": 3,
        "Plasmodium Vivax": 4
    }
    df.iloc[:, -1] = df.iloc[:, -1].map(label_map)
    
    X = df.iloc[:, :-1].values
    y = df.iloc[:, -1].values
    
    # Here: reshape X into 2D "images" for Conv2D
    # For Conv2D input, shape = (samples, height, width, channels)
    # You must decide how to reshape based on your data feature count
    
    # Example: if you have 1024 features, reshape to (32,32,1)
    # Replace this with actual size that matches your data!
    
    num_features = X.shape[1]
    size = int(np.sqrt(num_features))
    if size*size != num_features:
        raise ValueError(f"Cannot reshape {num_features} features into a square image. Please preprocess data accordingly.")
    
    X_reshaped = X.reshape(-1, size, size, 1)  # grayscale "images"
    
    y_cat = to_categorical(y, num_classes=5)
    
    X_train, X_test, y_train, y_test = train_test_split(X_reshaped, y_cat, test_size=0.25, random_state=42)
    
    model = build_model(input_shape=(size, size, 1))
    print(model.summary())
    
    history = model.fit(X_train, y_train, epochs=25, batch_size=32, validation_split=0.2, verbose=2)
    
    model.save('results/CNN.h5')
    
    test_loss, test_acc = model.evaluate(X_test, y_test)
    print(f"Test loss: {test_loss}, Test accuracy: {test_acc}")
    
    # Plot training history
    plt.plot(history.history['loss'], label='Train loss')
    plt.plot(history.history['val_loss'], label='Val loss')
    plt.legend()
    plt.show()
    
    plt.plot(history.history['accuracy'], label='Train accuracy')
    plt.plot(history.history['val_accuracy'], label='Val accuracy')
    plt.legend()
    plt.show()
    
    # Predict on test
    y_pred_prob = model.predict(X_test)
    y_pred = y_pred_prob.argmax(axis=1)
    y_true = y_test.argmax(axis=1)
    
    acc = accuracy_score(y_true, y_pred)
    mse = mean_squared_error(y_true, y_pred)
    mae = mean_absolute_error(y_true, y_pred)
    r2 = r2_score(y_true, y_pred)
    
    print(f"Accuracy: {acc}")
    print(f"MSE: {mse}")
    print(f"MAE: {mae}")
    print(f"R2 Score: {r2}")

# Uncomment to run training
# process()
