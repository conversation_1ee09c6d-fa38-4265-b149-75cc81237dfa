import cv2
import numpy as np
from tensorflow.keras.models import load_model

# Load the trained model
model = load_model('results/CNN_Conv2D.h5')

# Load the image for prediction
img_path = "path_to_image.jpg"  # Replace this with the actual image path
img = cv2.imread(img_path)
img = cv2.resize(img, (64, 64))               # Must match model input
img = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)   # Use if model trained on grayscale
img = img.astype('float32') / 255.0
img = img.reshape(1, 64, 64, 1)               # Shape: (1, height, width, channels)

# Predict
prediction = model.predict(img)
predicted_class = np.argmax(prediction)

# Map class index back to label
label_map = {
    0: "Normal",
    1: "Plasmodium Falciparum",
    2: "Plasmodium Malariae",
    3: "Plasmodium Ovale",
    4: "Plasmodium Vivax"
}

print("Predicted Class:", label_map[predicted_class])
