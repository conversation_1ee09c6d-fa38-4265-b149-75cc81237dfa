# -*- coding: utf-8 -*-

#importing libraries
import numpy as np
import matplotlib.pyplot as plt
import pandas as pd
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_squared_error
from sklearn.metrics import mean_absolute_error
from sklearn.metrics import r2_score
from sklearn.metrics import accuracy_score
from tensorflow.keras.utils import to_categorical
from keras.layers import Convolution1D, Dense, Dropout, MaxPooling1D, LSTM
from keras.models import Sequential
from keras.layers import Dense, Activation ,Dropout , Flatten , Conv1D ,MaxPooling1D
from keras.layers import LSTM
from keras import losses
from keras import optimizers
from timeit import default_timer as timer


def build_model(input):
    model = Sequential()
    model.add(Dense(128, input_shape=(input[1], input[2])))
    model.add(Conv1D(filters=112, kernel_size=1, padding='valid', activation='relu', kernel_initializer="uniform"))
    model.add(MaxPooling1D(pool_size=2, padding='valid'))
    model.add(Conv1D(filters=64, kernel_size=1, padding='valid', activation='relu', kernel_initializer="uniform"))
    model.add(MaxPooling1D(pool_size=1, padding='valid'))
    model.add(LSTM(70))
    model.add(Dropout(0.2))
    model.add(Flatten())
    model.add(Dense(100, activation="relu", kernel_initializer="uniform"))
    model.add(Dense(5, activation="softmax", kernel_initializer="uniform"))  # Changed to 'softmax' for classification
    model.compile(loss='categorical_crossentropy', optimizer='adam', metrics=['accuracy'])
    return model

def process():
    # Importing the dataset
    df = pd.read_csv("malariadataset.csv")
    
    # Replace labels with numeric values
    df = df.replace(to_replace="Normal", value=0)
    df = df.replace(to_replace="Plasmodium Falciparum", value=1)
    df = df.replace(to_replace="Plasmodium Malariae", value=2)
    df = df.replace(to_replace="Plasmodium Ovale", value=3)
    df = df.replace(to_replace="Plasmodium Vivax", value=4)
    
    print(df)
    
    x = df.iloc[:, :-1].values  # Features
    y = df.iloc[:, -1:].values  # Labels

    # Split the dataset into training set and test set
    x_train, x_test, y_train, y_test = train_test_split(x, y, test_size=0.25, random_state=0)

    # Reshape input for LSTM
    x_train = np.reshape(x_train, (x_train.shape[0], x_train.shape[1], 1))
    x_test = np.reshape(x_test, (x_test.shape[0], x_test.shape[1], 1))

    # Convert labels to categorical (one-hot encoding)
    y_train = to_categorical(y_train)
    y_test = to_categorical(y_test)

    # Build and summarize the model
    model = build_model([x_train.shape[0], x_train.shape[1], 1])
    print(model.summary())

    start = timer()
    
    # Train the model
    hist = model.fit(x_train, y_train, batch_size=128, epochs=25, validation_split=0.2, verbose=2)

    # Save the trained model
    model.save('results/CNNLSTM.h5')
    
    # Evaluate the model
    test_loss, test_acc = model.evaluate(x_test, y_test)
    print(f"Test Loss: {test_loss}, Test Accuracy: {test_acc}")

    # Plot train and validation loss
    plt.plot(hist.history['loss'])
    plt.plot(hist.history['val_loss'])
    plt.title('Model Loss')
    plt.ylabel('Loss')
    plt.xlabel('Epoch')
    plt.legend(['train', 'Validation'], loc='upper left')
    plt.savefig('results/CNNLSTM_Loss.png') 
    plt.pause(5)
    plt.show(block=False)
    plt.close()

    # Plot train and validation accuracy
    plt.plot(hist.history['accuracy'])
    plt.plot(hist.history['val_accuracy'])
    plt.title('Model Accuracy')
    plt.ylabel('Accuracy')
    plt.xlabel('Epoch')
    plt.legend(['train', 'Validation'], loc='upper left')
    plt.savefig('results/CNNLSTM_Accuracy.png') 
    plt.pause(5)
    plt.show(block=False)
    plt.close()

    # Predicting the Test set results
    y_pred1 = model.predict(x_test)
    print(y_pred1)
    y_pred = np.argmax(y_pred1, axis=1)
    print(y_pred)

    # Metrics Calculation
    mse = mean_squared_error(np.argmax(y_test, axis=1), y_pred)
    mae = mean_absolute_error(np.argmax(y_test, axis=1), y_pred)
    r2 = r2_score(np.argmax(y_test, axis=1), y_pred)

    print("---------------------------------------------------------")
    print(f"MSE VALUE FOR CNNLSTM IS {mse}")
    print(f"MAE VALUE FOR CNNLSTM IS {mae}")
    print(f"R-SQUARED VALUE FOR CNNLSTM IS {r2}")
    rms = np.sqrt(mse)
    print(f"RMSE VALUE FOR CNNLSTM IS {rms}")
    ac = accuracy_score(np.argmax(y_test, axis=1), y_pred)
    print(f"ACCURACY VALUE CNNLSTM IS {ac}")
    print("---------------------------------------------------------")

    # Save metrics to CSV
    with open("results/CNNLSTMMetrics.csv", 'w') as result2:
        result2.write("Parameter,Value\n")
        result2.write(f"MSE,{mse}\n")
        result2.write(f"MAE,{mae}\n")
        result2.write(f"R-SQUARED,{r2}\n")
        result2.write(f"RMSE,{rms}\n")
        result2.write(f"ACCURACY,{ac}\n")

    # Plot metrics bar chart
    df_metrics = pd.read_csv("results/CNNLSTMMetrics.csv")
    acc = df_metrics["Value"]
    alc = df_metrics["Parameter"]
    colors = ["#1f77b4", "#ff7f0e", "#2ca02c", "#d62728", "#8c564b"]
    
    fig = plt.figure()
    plt.bar(alc, acc, color=colors)
    plt.xlabel('Parameter')
    plt.ylabel('Value')
    plt.title("CNNLSTM Metrics Value")
    fig.savefig("results/CNNLSTMMetricsValue.png")
    plt.pause(5)
    plt.show(block=False)
    plt.close()

# Uncomment to run the process function
# process()
