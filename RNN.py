# -*- coding: utf-8 -*-

#importing libraries
import numpy as np
import matplotlib.pyplot as plt
import pandas as pd
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_squared_error
from sklearn.metrics import mean_absolute_error
from sklearn.metrics import r2_score
from sklearn.metrics import accuracy_score
from tensorflow.keras.utils import to_categorical  # Correct import for to_categorical
from sklearn.preprocessing import StandardScaler


from keras.models import Sequential
from keras.layers import Dense, Activation ,Dropout , Flatten , Conv1D ,MaxPooling1D
from keras.layers import LSTM
from keras import losses
from keras import optimizers
from timeit import default_timer as timer


def build_model(input):
    model = Sequential()
    model.add(LSTM(80, input_dim=2048))  
    model.add(Dropout(0.1))
    model.add(Dense(200, activation='relu'))
    model.add(Dropout(0.1))
    model.add(Dense(100, activation='relu'))
    model.add(Dense(5))
    model.add(Activation('softmax'))    
    model.compile(loss='mse', optimizer='adam', metrics=['accuracy'])
    return model

def process():
    # Importing the dataset
    df = pd.read_csv("malariadataset.csv")
    
    df = df.replace(to_replace="EOSINOPHIL", value=0)
    df = df.replace(to_replace="LYMPHOCYTE", value=1)
    df = df.replace(to_replace="MONOCYTE", value=2)
    df = df.replace(to_replace="NEUTROPHIL", value=3)
    
    print(df)
    
    x = df.iloc[:, :-1].values
    y = df.iloc[:, -1:].values

    # Scaling the data
    sc = StandardScaler()
    x_train, x_test, y_train, y_test = train_test_split(x, y, test_size=0.25, random_state=0)
    x_train = sc.fit_transform(x_train)
    x_test = sc.transform(x_test)

    # Reshaping the data for LSTM
    x_train = np.reshape(x_train, (x_train.shape[0], 1, x_train.shape[1]))
    x_test = np.reshape(x_test, (x_test.shape[0], 1, x_test.shape[1]))

    # Building and training the model
    model = build_model([x_train.shape[0], x_train.shape[1], 1])
    print(model.summary())

    # Using `to_categorical` for categorical labels
    y_train = to_categorical(y_train)
    y_test = to_categorical(y_test)

    start = timer()
    
    hist = model.fit(x_train, y_train, batch_size=128, epochs=25, validation_split=0.2, verbose=2)
    
    model.save('results/RNN.h5')

    # Model evaluation
    test_loss, test_acc = model.evaluate(x_test, y_test)
    print(f"Test Loss: {test_loss}, Test Accuracy: {test_acc}")

    # Plotting the loss
    plt.plot(hist.history['loss'])
    plt.plot(hist.history['val_loss'])
    plt.title('Model Loss')
    plt.ylabel('Loss')
    plt.xlabel('Epoch')
    plt.legend(['train', 'Validation'], loc='upper left')
    plt.savefig('results/RNN Loss.png')
    plt.pause(5)
    plt.show(block=False)
    plt.close()

    # Plotting the accuracy
    plt.plot(hist.history['accuracy'])
    plt.plot(hist.history['val_accuracy'])
    plt.title('Model Accuracy')
    plt.ylabel('Accuracy')
    plt.xlabel('Epoch')
    plt.legend(['train', 'Validation'], loc='upper left')
    plt.savefig('results/RNN Accuracy.png')
    plt.pause(5)
    plt.show(block=False)
    plt.close()

    # Predicting the Test set results
    y_pred1 = model.predict(x_test)
    y_pred = np.argmax(y_pred1, axis=1)

    # Metrics Calculation
    mse = mean_squared_error(y_test.argmax(axis=1), y_pred)
    mae = mean_absolute_error(y_test.argmax(axis=1), y_pred)
    r2 = r2_score(y_test.argmax(axis=1), y_pred)

    print("---------------------------------------------------------")
    print(f"MSE VALUE FOR RNN IS {mse}")
    print(f"MAE VALUE FOR RNN IS {mae}")
    print(f"R-SQUARED VALUE FOR RNN IS {r2}")
    rms = np.sqrt(mse)
    print(f"RMSE VALUE FOR RNN IS {rms}")
    ac = accuracy_score(y_test.argmax(axis=1), y_pred)
    print(f"ACCURACY VALUE RNN IS {ac}")
    print("---------------------------------------------------------")

    # Save metrics to CSV
    with open("results/RNNMetrics.csv", 'w') as result2:
        result2.write("Parameter,Value\n")
        result2.write(f"MSE,{mse}\n")
        result2.write(f"MAE,{mae}\n")
        result2.write(f"R-SQUARED,{r2}\n")
        result2.write(f"RMSE,{rms}\n")
        result2.write(f"ACCURACY,{ac}\n")

    # Plot metrics bar chart
    df_metrics = pd.read_csv("results/RNNMetrics.csv")
    acc = df_metrics["Value"]
    alc = df_metrics["Parameter"]
    colors = ["#1f77b4", "#ff7f0e", "#2ca02c", "#d62728", "#8c564b"]
    
    fig = plt.figure()
    plt.bar(alc, acc, color=colors)
    plt.xlabel('Parameter')
    plt.ylabel('Value')
    plt.title("RNN Metrics Value")
    fig.savefig("results/RNNMetricsValue.png")
    plt.pause(5)
    plt.show(block=False)
    plt.close()

# Call the process function if necessary
# process() 
