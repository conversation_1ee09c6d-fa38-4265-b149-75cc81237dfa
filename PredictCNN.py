import numpy as np
import pandas as pd

import cv2 as cv
import numpy as np
import FeatureExtract as ef
import os
import csv
from tensorflow.keras.utils import to_categorical

from keras.models import Sequential,load_model
from keras.layers import Dense, Activation ,Dropout , Flatten , Conv1D ,MaxPooling1D
from keras.layers import LSTM
from keras import losses
from keras import optimizers
from timeit import default_timer as timer
from sklearn.preprocessing import StandardScaler
from sklearn import preprocessing
from xgboost import XGBClassifier
from sklearn.model_selection import train_test_split



def segment(path):
	im0 = cv.imread(path)
	gray1 = cv.cvtColor(im0,cv.COLOR_BGR2GRAY)
	#cv.imshow('image1',gray1)

	#Using Otsu's thresholding after gaussian filtering 
	blur = cv.GaussianBlur(gray1,(5,5),0)
	ret, thresh = cv.threshold(blur,0,255,cv.THRESH_BINARY_INV+cv.THRESH_OTSU)

	#noise removal using grayscale morphology
	kernel = np.ones((3,3),np.uint8)

	closing = cv.morphologyEx(thresh,cv.MORPH_CLOSE,kernel,iterations = 5)
	#cv.imshow('image',closing)
	opening = cv.morphologyEx(closing,cv.MORPH_OPEN,kernel,iterations = 3)
	#cv.imshow('image2',opening)

	#sure background area
	sure_bg = cv.dilate(opening,kernel,iterations = 2)
	#cv.imshow('image5',sure_bg)
	#Finding sure foreground area
	dist_transform = cv.distanceTransform(opening,cv.DIST_L2,5)
	ret, sure_fg = cv.threshold(dist_transform,0.5*dist_transform.max(),255,0)
	#cv.imshow('image3',dist_transform)

	#Finding unknown region
	sure_fg = np.uint8(sure_fg)
	unknown = cv.subtract(sure_bg,sure_fg)
	#cv.imshow('image4',unknown)
	
	#Marker labelling
	ret, markers = cv.connectedComponents(sure_fg)
	#cv.imshow('image6',sure_fg)
	# Add one to all labels so that sure bacground is not 0, but 1
	markers = markers+1
	

	# Now, mark the region of unknown with zero
	markers[unknown==255] = 0
	#cv.imshow('image7',unknown)

	markers = cv.watershed(im0,markers)
	im0[markers == -1] = [255,0,0]

	#cv.imshow('image8',im0)
	gray2 = cv.cvtColor(im0,cv.COLOR_BGR2GRAY)
	im_color = cv.applyColorMap(gray2, cv.COLORMAP_JET)

	#cv.imshow('image9',im_color)
	cv.imwrite('result.jpg',im0)
	cv.imshow("Segmentaed image",im0)
	f=ef.extract_features("result.jpg")
	print(f)
	data=[]
	h=[]
	for g in f:
		h.append(g)
	data.append(h)
	return data

def build_model(input):
	model = Sequential()
	model.add(Dense(128,input_shape=(input[1],input[2])))
	model.add(Conv1D(filters = 112, kernel_size= 1,padding='valid', activation='relu', kernel_initializer="uniform"))
	model.add(MaxPooling1D(pool_size=2, padding='valid'))
	model.add(Conv1D(filters = 64,kernel_size = 1,padding='valid', activation='relu', kernel_initializer="uniform"))
	model.add(MaxPooling1D(pool_size=1, padding='valid'))
	model.add(Dropout(0.2))
	model.add(Flatten())
	model.add(Dense(100, activation="relu", kernel_initializer="uniform"))
	#model.add(Dropout(0.2))
	model.add(Dense(3, activation="relu", kernel_initializer="uniform"))
	model.compile(loss='mse',optimizer='adam',metrics=['accuracy'])
	return model

def process(path):
	# model = load_model('results/CNN.h5')
	df = pd.read_csv("malariadataset.csv")
	x = df.iloc[ : , :-1].values
	label_encoder = preprocessing.LabelEncoder()
	y = df.iloc[:, -1:].values
	y=label_encoder.fit_transform(y)
	print("y==",y)
	#Splitting the data into test and training sets
	X_train,X_test, y_train, y_test = train_test_split(x,y, test_size=0.2)
	#Fitting the RandomForestClassifier to the training set
	rfc = XGBClassifier(random_state = 0)
	rfc.fit(X_train, y_train)
	

	testdata=segment(path)
	X_test=np.array(testdata)
	#print(testdata)
	#print(X_test.shape)
	
	X_test = np.reshape(X_test, (1,X_test.shape[1])) 
	#print(X_test)
	#print(X_test.shape)
	prediction1 =rfc.predict(X_test)
	print("Prediction==",prediction1)
	

	res=""
	treat=""
	if prediction1[0]==0:
		res="No Disease"
		treat="Continue with your Regular Diet and excersice"
	if prediction1[0]==1:
		res="Plasmodium Falciparum"
		treat="Artemether + lumefantrine; artesunate + amodiaquine;\n artesunate + mefloquine; artesunate + sulfadoxine-pyrimethamine, and dihydroartemisinin + piperaquine .\n Quinine plus tetracycline or doxycycline or clindamycin"
	if prediction1[0]==2:
		res="Plasmodium Malariae"
		treat="Artemisinin drugs (artemether and artesunate)"
	if prediction1[0]==3:
		res="Plasmodium Ovale"
		treat="Chloroquine and or an artemisinin combination therapy (ACT) are used to treat P. ovale"
	if prediction1[0]==4:
		res="Plasmodium Vivax"
		treat="chloroquine in full therapeutic dose of 25 mg/kg divided over three days."
	

	return res,treat
#print("Result==",process("Training/Normal/Normal case (106).jpg"))

