
import tkinter as tk
from tkinter import Message, Text, Frame
import shutil
import csv
import numpy as np
from PIL import Image, ImageTk
import pandas as pd
import datetime
import time
import tkinter.font as font
from tkinter import filedialog
import tkinter.messagebox as tm
import CNN as nn
import RNN as rnn
import <PERSON><PERSON><PERSON> as clstm
import PredictCNN as pre
import Segmentation as cd
from tkinter import ttk
import os

# Improved color scheme - more professional and modern
PRIMARY_COLOR = "#2E86AB"      # Professional blue
SECONDARY_COLOR = "#A23B72"    # Accent purple
BACKGROUND_COLOR = "#F18F01"   # Warm orange (similar to original but refined)
LIGHT_BG = "#F5F5F5"          # Light gray background
WHITE = "#FFFFFF"
DARK_TEXT = "#2C3E50"
BUTTON_HOVER = "#1E5F7A"      # Darker blue for hover

# Global variables
selected_image_path = ""
preview_image = None

# Create main window with improved styling
window = tk.Tk()
window.title("🔬 Malaria Detection System")
window.geometry('1200x700')
window.configure(background=LIGHT_BG)
window.resizable(True, True)

# Center the window on screen
window.update_idletasks()
x = (window.winfo_screenwidth() // 2) - (1200 // 2)
y = (window.winfo_screenheight() // 2) - (700 // 2)
window.geometry(f'1200x700+{x}+{y}')

# Configure grid for responsive design
window.grid_rowconfigure(1, weight=1)
window.grid_columnconfigure(0, weight=1)

# Create header frame with gradient-like effect
header_frame = Frame(window, bg=PRIMARY_COLOR, height=80)
header_frame.grid(row=0, column=0, sticky="ew", padx=20, pady=(20, 10))
header_frame.grid_propagate(False)
header_frame.grid_columnconfigure(0, weight=1)

# Main title with improved styling
title_label = tk.Label(
    header_frame,
    text="🔬 Malaria Detection System",
    bg=PRIMARY_COLOR,
    fg=WHITE,
    font=('Arial', 24, 'bold'),
    pady=25
)
title_label.grid(row=0, column=0)

# Create main content frame
main_frame = Frame(window, bg=LIGHT_BG)
main_frame.grid(row=1, column=0, sticky="nsew", padx=20, pady=10)
main_frame.grid_rowconfigure(0, weight=1)
main_frame.grid_columnconfigure(0, weight=1)
main_frame.grid_columnconfigure(1, weight=1)

# Left panel for image selection
left_panel = Frame(main_frame, bg=WHITE, relief="raised", bd=1)
left_panel.grid(row=0, column=0, sticky="nsew", padx=(0, 10))
left_panel.grid_rowconfigure(2, weight=1)

# Left panel header
left_header = Frame(left_panel, bg=SECONDARY_COLOR, height=50)
left_header.grid(row=0, column=0, sticky="ew")
left_header.grid_propagate(False)
left_header.grid_columnconfigure(0, weight=1)

tk.Label(
    left_header,
    text="📁 Image Selection",
    bg=SECONDARY_COLOR,
    fg=WHITE,
    font=('Arial', 14, 'bold'),
    pady=12
).grid(row=0, column=0)

# Image selection controls
controls_frame = Frame(left_panel, bg=WHITE, padx=20, pady=20)
controls_frame.grid(row=1, column=0, sticky="ew")
controls_frame.grid_columnconfigure(1, weight=1)

tk.Label(
    controls_frame,
    text="Select Blood Cell Image:",
    bg=WHITE,
    fg=DARK_TEXT,
    font=('Arial', 11, 'bold')
).grid(row=0, column=0, columnspan=3, sticky="w", pady=(0, 10))

# File path entry with improved styling
file_path_var = tk.StringVar()
txt1 = tk.Entry(
    controls_frame,
    textvariable=file_path_var,
    font=('Arial', 10),
    bg=LIGHT_BG,
    fg=DARK_TEXT,
    relief="solid",
    bd=1,
    width=35
)
txt1.grid(row=1, column=0, columnspan=2, sticky="ew", pady=(0, 15))

# Modern button styling function
def create_button(parent, text, command, bg_color, row, col, width=15):
    btn = tk.Button(
        parent,
        text=text,
        command=command,
        bg=bg_color,
        fg=WHITE,
        font=('Arial', 10, 'bold'),
        relief="flat",
        bd=0,
        padx=15,
        pady=8,
        cursor="hand2",
        width=width
    )
    btn.grid(row=row, column=col, padx=5, pady=5, sticky="ew")

    # Add hover effects
    def on_enter(e):
        btn.config(bg=BUTTON_HOVER)
    def on_leave(e):
        btn.config(bg=bg_color)

    btn.bind("<Enter>", on_enter)
    btn.bind("<Leave>", on_leave)
    return btn

# Browse button
browse_btn = create_button(controls_frame, "📂 Browse", None, PRIMARY_COLOR, 1, 2, 12)

# Image preview area
preview_frame = Frame(left_panel, bg=WHITE, padx=20, pady=10)
preview_frame.grid(row=2, column=0, sticky="nsew")
preview_frame.grid_rowconfigure(1, weight=1)
preview_frame.grid_columnconfigure(0, weight=1)

tk.Label(
    preview_frame,
    text="Image Preview:",
    bg=WHITE,
    fg=DARK_TEXT,
    font=('Arial', 11, 'bold')
).grid(row=0, column=0, sticky="w", pady=(0, 10))

# Preview label for image
preview_label = tk.Label(
    preview_frame,
    text="No image selected\n\nClick 'Browse' to select\na blood cell image",
    bg=LIGHT_BG,
    fg=DARK_TEXT,
    font=('Arial', 10),
    relief="sunken",
    bd=1,
    width=40,
    height=15,
    justify="center"
)
preview_label.grid(row=1, column=0, sticky="nsew", pady=(0, 10))

# Right panel for results
right_panel = Frame(main_frame, bg=WHITE, relief="raised", bd=1)
right_panel.grid(row=0, column=1, sticky="nsew", padx=(10, 0))
right_panel.grid_rowconfigure(2, weight=1)

# Right panel header
right_header = Frame(right_panel, bg=BACKGROUND_COLOR, height=50)
right_header.grid(row=0, column=0, sticky="ew")
right_header.grid_propagate(False)
right_header.grid_columnconfigure(0, weight=1)

tk.Label(
    right_header,
    text="🔬 Analysis Results",
    bg=BACKGROUND_COLOR,
    fg=WHITE,
    font=('Arial', 14, 'bold'),
    pady=12
).grid(row=0, column=0)

# Results display area
results_frame = Frame(right_panel, bg=WHITE, padx=20, pady=20)
results_frame.grid(row=1, column=0, sticky="ew")

# Status label
status_label = tk.Label(
    results_frame,
    text="Status: Ready for analysis",
    bg=WHITE,
    fg=DARK_TEXT,
    font=('Arial', 11, 'bold')
)
status_label.grid(row=0, column=0, sticky="w", pady=(0, 15))

# Results text area
results_text = Text(
    results_frame,
    height=20,
    width=50,
    bg=LIGHT_BG,
    fg=DARK_TEXT,
    font=('Arial', 10),
    relief="solid",
    bd=1,
    wrap="word",
    state="disabled"
)
results_text.grid(row=1, column=0, sticky="ew", pady=(0, 15))

# Control buttons frame
buttons_frame = Frame(main_frame, bg=LIGHT_BG)
buttons_frame.grid(row=1, column=0, columnspan=2, pady=20)
buttons_frame.grid_columnconfigure(0, weight=1)
buttons_frame.grid_columnconfigure(1, weight=1)
buttons_frame.grid_columnconfigure(2, weight=1)

# Function implementations
def clear():
    """Clear all inputs and results"""
    global selected_image_path, preview_image
    selected_image_path = ""
    preview_image = None
    file_path_var.set("")

    # Reset preview
    preview_label.config(
        text="No image selected\n\nClick 'Browse' to select\na blood cell image",
        image=""
    )

    # Clear results
    results_text.config(state="normal")
    results_text.delete(1.0, tk.END)
    results_text.insert(tk.END, "Results cleared.\n\nSelect an image to begin analysis.")
    results_text.config(state="disabled")

    status_label.config(text="Status: Ready for analysis", fg=DARK_TEXT)

def browse_image():
    """Browse and select an image file"""
    global selected_image_path, preview_image

    file_types = [
        ("Image files", "*.jpg *.jpeg *.png *.bmp *.tiff *.tif"),
        ("All files", "*.*")
    ]

    file_path = filedialog.askopenfilename(
        title="Select Blood Cell Image",
        filetypes=file_types
    )

    if file_path:
        selected_image_path = file_path
        file_path_var.set(file_path)

        try:
            # Load and display image preview
            img = Image.open(file_path)

            # Resize image for preview while maintaining aspect ratio
            img.thumbnail((300, 200), Image.Resampling.LANCZOS)
            preview_image = ImageTk.PhotoImage(img)

            preview_label.config(
                image=preview_image,
                text="",
                compound="center"
            )

            status_label.config(
                text=f"Status: Image loaded - {os.path.basename(file_path)}",
                fg="#27AE60"
            )

            # Update results
            results_text.config(state="normal")
            results_text.delete(1.0, tk.END)
            results_text.insert(tk.END, f"Image loaded successfully!\n\nFile: {os.path.basename(file_path)}\nPath: {file_path}\n\nClick 'Analyze' to start malaria detection.")
            results_text.config(state="disabled")

        except Exception as e:
            tm.showerror("Error", f"Failed to load image: {str(e)}")
            selected_image_path = ""
            file_path_var.set("")

def predict():
    """Perform malaria prediction"""
    global selected_image_path

    if not selected_image_path:
        tm.showerror("Error", "Please select an image first!")
        return

    try:
        status_label.config(text="Status: Analyzing image...", fg="#E67E22")
        window.update()

        # Perform prediction
        result, treatment = pre.process(selected_image_path)

        # Update results display
        results_text.config(state="normal")
        results_text.delete(1.0, tk.END)

        result_text = f"""MALARIA DETECTION RESULTS
{'='*40}

ANALYSIS RESULT: {result}

TREATMENT RECOMMENDATION:
{treatment}

ANALYSIS DETAILS:
• Image: {os.path.basename(selected_image_path)}
• Date: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
• Model: AI-based Classification

DISCLAIMER: This is an AI analysis tool for research purposes.
Always consult with medical professionals for proper diagnosis.
"""

        results_text.insert(tk.END, result_text)
        results_text.config(state="disabled")

        # Update status
        if "Normal" in str(result):
            status_label.config(text="Status: Analysis complete - Normal cells", fg="#27AE60")
        else:
            status_label.config(text="Status: Analysis complete - Malaria detected", fg="#E74C3C")

        # Show popup
        tm.showinfo("Analysis Complete", f"Result: {result}\n\nTreatment: {treatment}")

    except Exception as e:
        error_msg = f"Error during analysis: {str(e)}"
        results_text.config(state="normal")
        results_text.delete(1.0, tk.END)
        results_text.insert(tk.END, f"ANALYSIS ERROR\n\n{error_msg}\n\nPlease try again with a different image.")
        results_text.config(state="disabled")
        status_label.config(text="Status: Analysis failed", fg="#E74C3C")
        tm.showerror("Analysis Error", error_msg)

# Connect button commands
browse_btn.config(command=browse_image)

# Create control buttons
clear_btn = create_button(buttons_frame, "🗑️ Clear", clear, SECONDARY_COLOR, 0, 0, 15)
predict_btn = create_button(buttons_frame, "🔍 Analyze", predict, "#27AE60", 0, 1, 15)
quit_btn = create_button(buttons_frame, "❌ Exit", window.destroy, "#E74C3C", 0, 2, 15)

# Initialize results display
results_text.config(state="normal")
results_text.insert(tk.END, "Welcome to Malaria Detection System!\n\nThis AI-powered tool analyzes blood cell images to detect malaria parasites.\n\nTo get started:\n1. Click 'Browse' to select an image\n2. Click 'Analyze' to start detection\n3. View results and recommendations\n\nSupported formats: JPG, PNG, BMP, TIFF")
results_text.config(state="disabled")

# Start the application
window.mainloop()
