
import tkinter as tk
from tkinter import Message ,Text
import shutil
import csv
import numpy as np
from PIL import Image, ImageTk
import pandas as pd
import datetime
import time
import tkinter.font as font
from tkinter import filedialog
import tkinter.messagebox as tm
import CNN as nn
import RNN as rnn
import CNNLSTM as clstm
import PredictCNN as pre
import Segmentation as cd
from tkinter import ttk



fontScale=1
fontColor=(0,0,0)
cond=0

bgcolor="#EB5406"
fgcolor="white"

window = tk.Tk()
window.title("Malaria Prediction")

 
window.geometry('1280x720')
window.configure(background=bgcolor)
#window.attributes('-fullscreen', True)

window.grid_rowconfigure(0, weight=1)
window.grid_columnconfigure(0, weight=1)

# Load the background image
#background_image = Image.open("C:\\Users\\<USER>\\Downloads\\th (2).jpeg")  # Replace "background_image.jpg" with the path to your image
#background_image = background_image.resize((1280, 720), Image.ANTIALIAS)  # Resize the image to fit the window
#background_photo = ImageTk.PhotoImage(background_image)

    # Create a label with the background image
#background_label = tk.Label(window, image=background_photo)
#background_label.image = background_photo
#background_label.place(x=0, y=0, relwidth=1, relheight=1)


message1 = tk.Label(window, text="Malaria Prediction" ,bg=bgcolor  ,fg=fgcolor  ,width=50  ,height=3,font=('times', 30, 'italic bold underline')) 
message1.place(x=100, y=10)

#lbl = tk.Label(window, text="Select Dataset",width=20  ,height=2  ,fg=fgcolor  ,bg=bgcolor ,font=('times', 15, ' bold ') ) 
#lbl.place(x=10, y=200)

#txt = tk.Entry(window,width=20,bg="white" ,fg="black",font=('times', 15, ' bold '))
#txt.place(x=300, y=215)


lbl1 = tk.Label(window, text="Select Image",width=20  ,height=2  ,fg=fgcolor  ,bg=bgcolor ,font=('times', 15, ' bold ') ) 
lbl1.place(x=10, y=200)

txt1 = tk.Entry(window,width=20,bg="white" ,fg="black",font=('times', 15, ' bold '))
txt1.place(x=300, y=215)



def clear():
	#txt.delete(0, 'end') 
	txt1.delete(0, 'end')

def browse():
	path=filedialog.askdirectory()
	print(path)
	txt.delete(0, 'end') 
	txt.insert('end',path)
	if path !="":
		print(path)
	else:
		tm.showinfo("Input error", "Select Dataset Folder")	

def browse1():
	path=filedialog.askopenfilename()
	print(path)
	txt1.delete(0, 'end') 

	txt1.insert('end',path)
	if path !="":
		print(path)
	else:
		tm.showinfo("Input error", "Select image")	

# def CNN():
# 	nn.process()
# 	tm.showinfo("Input", "CNN Successfully Finished")

def predict():
	sym1=txt1.get()
	if sym1 != "":
		res,treat=pre.process(sym1)
		tm.showinfo("Output", "Class  " + str(res))
		tm.showinfo("Treatment Recommendation", str(treat))
	else:
		tm.showinfo("Input error", "Select Image")
		
		
clearButton = tk.Button(window, text="Clear", command=clear  ,fg=fgcolor  ,bg=bgcolor  ,width=20  ,height=2 ,activebackground = "Red" ,font=('times', 15, ' bold '))
clearButton.place(x=830, y=200)

#browse = tk.Button(window, text="Browse", command=browse  ,fg=fgcolor  ,bg=bgcolor  ,width=15  ,height=1, activebackground = "Red" ,font=('times', 15, ' bold '))
#browse.place(x=530, y=205)

browse1 = tk.Button(window, text="Browse", command=browse1  ,fg=fgcolor  ,bg=bgcolor  ,width=15  ,height=1, activebackground = "Red" ,font=('times', 15, ' bold '))
browse1.place(x=530, y=205)

# pre1 = tk.Button(window, text="CNN", command=CNN  ,fg=fgcolor  ,bg=bgcolor  ,width=18  ,height=2, activebackground = "Red" ,font=('times', 15, ' bold '))
# pre1.place(x=150, y=500)

qr = tk.Button(window, text="Predict", command=predict  ,fg=fgcolor ,bg=bgcolor  ,width=18  ,height=2, activebackground = "Red" ,font=('times', 15, ' bold '))
qr.place(x=500, y=500)


quitWindow = tk.Button(window, text="QUIT", command=window.destroy  ,fg=fgcolor ,bg=bgcolor  ,width=18  ,height=2, activebackground = "Red" ,font=('times', 15, ' bold '))
quitWindow.place(x=900, y=500)

 
window.mainloop()
