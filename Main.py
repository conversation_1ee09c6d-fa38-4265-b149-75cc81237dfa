
import tkinter as tk
from tkinter import Message, Text, Canvas, Frame
import shutil
import csv
import numpy as np
from PIL import Image, ImageTk
import pandas as pd
import datetime
import time
import tkinter.font as font
from tkinter import filedialog
import tkinter.messagebox as tm
import CNN as nn
import RNN as rnn
import <PERSON>LS<PERSON> as clstm
import PredictCNN as pre
import Segmentation as cd
from tkinter import ttk
import os

# Global variables
selected_image_path = ""
preview_image = None

# Modern color scheme
PRIMARY_COLOR = "#2C3E50"      # Dark blue-gray
SECONDARY_COLOR = "#3498DB"    # Bright blue
ACCENT_COLOR = "#E74C3C"       # Red
SUCCESS_COLOR = "#27AE60"      # Green
WARNING_COLOR = "#F39C12"      # Orange
LIGHT_BG = "#ECF0F1"          # Light gray
WHITE = "#FFFFFF"
DARK_TEXT = "#2C3E50"
LIGHT_TEXT = "#FFFFFF"

# Create main window
window = tk.Tk()
window.title("🔬 Malaria Detection System")
window.geometry('1400x800')
window.configure(background=LIGHT_BG)
window.resizable(True, True)

# Configure grid weights for responsive design
window.grid_rowconfigure(0, weight=1)
window.grid_columnconfigure(0, weight=1)

# Create main container with padding
main_container = Frame(window, bg=LIGHT_BG, padx=30, pady=20)
main_container.grid(row=0, column=0, sticky="nsew")
main_container.grid_rowconfigure(1, weight=1)
main_container.grid_columnconfigure(0, weight=1)

# Header section
header_frame = Frame(main_container, bg=PRIMARY_COLOR, height=100)
header_frame.grid(row=0, column=0, sticky="ew", pady=(0, 20))
header_frame.grid_propagate(False)
header_frame.grid_columnconfigure(0, weight=1)

# Title with modern styling
title_label = tk.Label(
    header_frame,
    text="🔬 Malaria Detection System",
    bg=PRIMARY_COLOR,
    fg=LIGHT_TEXT,
    font=('Segoe UI', 28, 'bold'),
    pady=20
)
title_label.grid(row=0, column=0)

subtitle_label = tk.Label(
    header_frame,
    text="AI-Powered Blood Cell Analysis for Malaria Detection",
    bg=PRIMARY_COLOR,
    fg=LIGHT_TEXT,
    font=('Segoe UI', 12, 'italic'),
    pady=(0, 10)
)
subtitle_label.grid(row=1, column=0)

# Main content area
content_frame = Frame(main_container, bg=LIGHT_BG)
content_frame.grid(row=1, column=0, sticky="nsew", pady=(0, 20))
content_frame.grid_rowconfigure(0, weight=1)
content_frame.grid_columnconfigure(0, weight=1)
content_frame.grid_columnconfigure(1, weight=1)



# Left panel - Image Upload Section
left_panel = Frame(content_frame, bg=WHITE, relief="raised", bd=2)
left_panel.grid(row=0, column=0, sticky="nsew", padx=(0, 10))
left_panel.grid_rowconfigure(2, weight=1)
left_panel.grid_columnconfigure(0, weight=1)

# Left panel header
left_header = Frame(left_panel, bg=SECONDARY_COLOR, height=60)
left_header.grid(row=0, column=0, sticky="ew")
left_header.grid_propagate(False)
left_header.grid_columnconfigure(0, weight=1)

tk.Label(
    left_header,
    text="📁 Image Upload",
    bg=SECONDARY_COLOR,
    fg=LIGHT_TEXT,
    font=('Segoe UI', 16, 'bold'),
    pady=15
).grid(row=0, column=0)

# File selection area
file_frame = Frame(left_panel, bg=WHITE, padx=20, pady=20)
file_frame.grid(row=1, column=0, sticky="ew")
file_frame.grid_columnconfigure(1, weight=1)

tk.Label(
    file_frame,
    text="Select Blood Cell Image:",
    bg=WHITE,
    fg=DARK_TEXT,
    font=('Segoe UI', 12, 'bold')
).grid(row=0, column=0, columnspan=3, sticky="w", pady=(0, 10))

# File path entry
file_path_var = tk.StringVar()
file_entry = tk.Entry(
    file_frame,
    textvariable=file_path_var,
    font=('Segoe UI', 11),
    bg=LIGHT_BG,
    fg=DARK_TEXT,
    relief="flat",
    bd=5
)
file_entry.grid(row=1, column=0, columnspan=2, sticky="ew", pady=(0, 10))

# Browse button with modern styling
def create_modern_button(parent, text, command, bg_color, fg_color=LIGHT_TEXT, width=15):
    btn = tk.Button(
        parent,
        text=text,
        command=command,
        bg=bg_color,
        fg=fg_color,
        font=('Segoe UI', 11, 'bold'),
        relief="flat",
        bd=0,
        padx=20,
        pady=10,
        cursor="hand2",
        activebackground=bg_color,
        activeforeground=fg_color,
        width=width
    )
    return btn

browse_btn = create_modern_button(file_frame, "📂 Browse", None, SECONDARY_COLOR)
browse_btn.grid(row=1, column=2, padx=(10, 0), pady=(0, 10))

# Image preview area
preview_frame = Frame(left_panel, bg=WHITE, padx=20, pady=10)
preview_frame.grid(row=2, column=0, sticky="nsew")
preview_frame.grid_rowconfigure(1, weight=1)
preview_frame.grid_columnconfigure(0, weight=1)

tk.Label(
    preview_frame,
    text="Image Preview:",
    bg=WHITE,
    fg=DARK_TEXT,
    font=('Segoe UI', 12, 'bold')
).grid(row=0, column=0, sticky="w", pady=(0, 10))

# Canvas for image preview
preview_canvas = Canvas(
    preview_frame,
    bg=LIGHT_BG,
    relief="sunken",
    bd=2,
    width=300,
    height=300
)
preview_canvas.grid(row=1, column=0, sticky="nsew", pady=(0, 10))

# Placeholder text
preview_canvas.create_text(
    150, 150,
    text="No image selected\n\nClick 'Browse' to select\na blood cell image",
    fill=DARK_TEXT,
    font=('Segoe UI', 11),
    justify="center"
)

# Right panel - Results Section
right_panel = Frame(content_frame, bg=WHITE, relief="raised", bd=2)
right_panel.grid(row=0, column=1, sticky="nsew", padx=(10, 0))
right_panel.grid_rowconfigure(2, weight=1)
right_panel.grid_columnconfigure(0, weight=1)

# Right panel header
right_header = Frame(right_panel, bg=SUCCESS_COLOR, height=60)
right_header.grid(row=0, column=0, sticky="ew")
right_header.grid_propagate(False)
right_header.grid_columnconfigure(0, weight=1)

tk.Label(
    right_header,
    text="🔬 Analysis Results",
    bg=SUCCESS_COLOR,
    fg=LIGHT_TEXT,
    font=('Segoe UI', 16, 'bold'),
    pady=15
).grid(row=0, column=0)

# Results display area
results_frame = Frame(right_panel, bg=WHITE, padx=20, pady=20)
results_frame.grid(row=1, column=0, sticky="ew")
results_frame.grid_columnconfigure(0, weight=1)

# Status indicator
status_frame = Frame(results_frame, bg=WHITE)
status_frame.grid(row=0, column=0, sticky="ew", pady=(0, 20))
status_frame.grid_columnconfigure(1, weight=1)

tk.Label(
    status_frame,
    text="Status:",
    bg=WHITE,
    fg=DARK_TEXT,
    font=('Segoe UI', 12, 'bold')
).grid(row=0, column=0, sticky="w")

status_label = tk.Label(
    status_frame,
    text="Ready for analysis",
    bg=WHITE,
    fg=WARNING_COLOR,
    font=('Segoe UI', 11)
)
status_label.grid(row=0, column=1, sticky="w", padx=(10, 0))

# Results text area
results_text = Text(
    results_frame,
    height=15,
    width=40,
    bg=LIGHT_BG,
    fg=DARK_TEXT,
    font=('Segoe UI', 11),
    relief="flat",
    bd=5,
    wrap="word",
    state="disabled"
)
results_text.grid(row=1, column=0, sticky="ew", pady=(0, 20))

# Scrollbar for results
scrollbar = ttk.Scrollbar(results_frame, orient="vertical", command=results_text.yview)
scrollbar.grid(row=1, column=1, sticky="ns", pady=(0, 20))
results_text.configure(yscrollcommand=scrollbar.set)

# Control buttons area
control_frame = Frame(main_container, bg=LIGHT_BG)
control_frame.grid(row=2, column=0, sticky="ew")
control_frame.grid_columnconfigure(0, weight=1)
control_frame.grid_columnconfigure(1, weight=1)
control_frame.grid_columnconfigure(2, weight=1)
control_frame.grid_columnconfigure(3, weight=1)

# Action buttons with modern styling
predict_btn = create_modern_button(control_frame, "🔍 Analyze Image", None, SUCCESS_COLOR, width=20)
predict_btn.grid(row=0, column=0, padx=10, pady=10)

clear_btn = create_modern_button(control_frame, "🗑️ Clear", None, WARNING_COLOR, width=15)
clear_btn.grid(row=0, column=1, padx=10, pady=10)

save_btn = create_modern_button(control_frame, "💾 Save Results", None, SECONDARY_COLOR, width=15)
save_btn.grid(row=0, column=2, padx=10, pady=10)

quit_btn = create_modern_button(control_frame, "❌ Exit", window.destroy, ACCENT_COLOR, width=15)
quit_btn.grid(row=0, column=3, padx=10, pady=10)

# Progress bar
progress_frame = Frame(main_container, bg=LIGHT_BG)
progress_frame.grid(row=3, column=0, sticky="ew", pady=(10, 0))
progress_frame.grid_columnconfigure(0, weight=1)

progress_bar = ttk.Progressbar(
    progress_frame,
    mode='indeterminate',
    length=400
)
progress_bar.grid(row=0, column=0, pady=10)

progress_label = tk.Label(
    progress_frame,
    text="",
    bg=LIGHT_BG,
    fg=DARK_TEXT,
    font=('Segoe UI', 10)
)
progress_label.grid(row=1, column=0)

# Function implementations
def update_results_text(text, color=DARK_TEXT):
    """Update the results text area with new content"""
    results_text.config(state="normal")
    results_text.delete(1.0, tk.END)
    results_text.insert(tk.END, text)
    results_text.config(state="disabled", fg=color)

def update_status(text, color=WARNING_COLOR):
    """Update the status label"""
    status_label.config(text=text, fg=color)

def show_progress(text="Processing..."):
    """Show progress bar with text"""
    progress_label.config(text=text)
    progress_bar.start(10)
    window.update()

def hide_progress():
    """Hide progress bar"""
    progress_bar.stop()
    progress_label.config(text="")
    window.update()

def load_and_preview_image(image_path):
    """Load and display image preview"""
    global preview_image
    try:
        # Open and resize image for preview
        img = Image.open(image_path)

        # Calculate size to fit in preview canvas while maintaining aspect ratio
        canvas_width = 300
        canvas_height = 300
        img_width, img_height = img.size

        # Calculate scaling factor
        scale = min(canvas_width/img_width, canvas_height/img_height)
        new_width = int(img_width * scale)
        new_height = int(img_height * scale)

        # Resize image
        img = img.resize((new_width, new_height), Image.Resampling.LANCZOS)
        preview_image = ImageTk.PhotoImage(img)

        # Clear canvas and display image
        preview_canvas.delete("all")
        x = (canvas_width - new_width) // 2
        y = (canvas_height - new_height) // 2
        preview_canvas.create_image(x, y, anchor="nw", image=preview_image)

        # Add image info
        info_text = f"Image loaded: {os.path.basename(image_path)}\nSize: {img_width}x{img_height} pixels"
        preview_canvas.create_text(
            canvas_width//2, canvas_height-30,
            text=info_text,
            fill=DARK_TEXT,
            font=('Segoe UI', 9),
            justify="center"
        )

        return True
    except Exception as e:
        tm.showerror("Error", f"Failed to load image: {str(e)}")
        return False

def browse_image():
    """Browse and select an image file"""
    global selected_image_path

    file_types = [
        ("Image files", "*.jpg *.jpeg *.png *.bmp *.tiff *.tif"),
        ("JPEG files", "*.jpg *.jpeg"),
        ("PNG files", "*.png"),
        ("All files", "*.*")
    ]

    file_path = filedialog.askopenfilename(
        title="Select Blood Cell Image",
        filetypes=file_types
    )

    if file_path:
        selected_image_path = file_path
        file_path_var.set(file_path)

        if load_and_preview_image(file_path):
            update_status("Image loaded - Ready for analysis", SUCCESS_COLOR)
            update_results_text("Image successfully loaded.\n\nClick 'Analyze Image' to start the malaria detection process.")
        else:
            selected_image_path = ""
            file_path_var.set("")
            update_status("Failed to load image", ACCENT_COLOR)

def clear_all():
    """Clear all inputs and results"""
    global selected_image_path, preview_image

    selected_image_path = ""
    preview_image = None
    file_path_var.set("")

    # Clear preview canvas
    preview_canvas.delete("all")
    preview_canvas.create_text(
        150, 150,
        text="No image selected\n\nClick 'Browse' to select\na blood cell image",
        fill=DARK_TEXT,
        font=('Segoe UI', 11),
        justify="center"
    )

    # Clear results
    update_results_text("Results cleared.\n\nSelect an image to begin analysis.")
    update_status("Ready for analysis", WARNING_COLOR)
    hide_progress()

def predict_malaria():
    """Perform malaria prediction on selected image"""
    global selected_image_path

    if not selected_image_path:
        tm.showerror("Error", "Please select an image first!")
        return

    try:
        # Show progress
        show_progress("Analyzing image...")
        update_status("Processing...", SECONDARY_COLOR)

        # Perform prediction
        result, treatment = pre.process(selected_image_path)

        # Hide progress
        hide_progress()

        # Format results
        result_text = f"""🔬 MALARIA DETECTION RESULTS
{'='*50}

📊 ANALYSIS RESULT:
{result}

💊 TREATMENT RECOMMENDATION:
{treatment}

📋 ANALYSIS DETAILS:
• Image: {os.path.basename(selected_image_path)}
• Analysis Date: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
• Model: CNN-based Classification
• Status: Analysis Complete

⚠️  IMPORTANT DISCLAIMER:
This is an AI-assisted analysis tool for research purposes.
Always consult with a qualified medical professional for
proper diagnosis and treatment decisions.
"""

        # Update results display
        update_results_text(result_text)

        # Update status based on result
        if "Normal" in str(result):
            update_status("Analysis Complete - Normal cells detected", SUCCESS_COLOR)
        else:
            update_status("Analysis Complete - Malaria parasites detected", ACCENT_COLOR)

        # Show result in popup as well
        tm.showinfo("Analysis Complete", f"Result: {result}\n\nTreatment: {treatment}")

    except Exception as e:
        hide_progress()
        error_msg = f"Error during analysis: {str(e)}"
        update_results_text(f"❌ ANALYSIS ERROR\n\n{error_msg}\n\nPlease try again with a different image or check the image format.")
        update_status("Analysis failed", ACCENT_COLOR)
        tm.showerror("Analysis Error", error_msg)

def save_results():
    """Save analysis results to file"""
    if not results_text.get(1.0, tk.END).strip():
        tm.showwarning("Warning", "No results to save!")
        return

    try:
        file_path = filedialog.asksaveasfilename(
            title="Save Results",
            defaultextension=".txt",
            filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
        )

        if file_path:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(results_text.get(1.0, tk.END))
            tm.showinfo("Success", f"Results saved to:\n{file_path}")
    except Exception as e:
        tm.showerror("Error", f"Failed to save results: {str(e)}")

# Connect button commands
browse_btn.config(command=browse_image)
predict_btn.config(command=predict_malaria)
clear_btn.config(command=clear_all)
save_btn.config(command=save_results)

# Add hover effects for buttons
def on_enter(event, button, hover_color):
    button.config(bg=hover_color)

def on_leave(event, button, normal_color):
    button.config(bg=normal_color)

# Apply hover effects
buttons_config = [
    (browse_btn, SECONDARY_COLOR, "#2980B9"),
    (predict_btn, SUCCESS_COLOR, "#229954"),
    (clear_btn, WARNING_COLOR, "#E67E22"),
    (save_btn, SECONDARY_COLOR, "#2980B9"),
    (quit_btn, ACCENT_COLOR, "#C0392B")
]

for btn, normal_color, hover_color in buttons_config:
    btn.bind("<Enter>", lambda e, b=btn, hc=hover_color: on_enter(e, b, hc))
    btn.bind("<Leave>", lambda e, b=btn, nc=normal_color: on_leave(e, b, nc))

# Initialize the application
update_results_text("🔬 Welcome to Malaria Detection System\n\nThis AI-powered tool analyzes blood cell images to detect malaria parasites.\n\nTo get started:\n1. Click 'Browse' to select a blood cell image\n2. Click 'Analyze Image' to start detection\n3. View results and treatment recommendations\n\nSupported formats: JPG, PNG, BMP, TIFF")
update_status("Ready - Select an image to begin", SUCCESS_COLOR)

# Start the application
window.mainloop()
