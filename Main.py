
import tkinter as tk
from tkinter import Message, Text, Frame, Canvas, Scrollbar
import shutil
import csv
import numpy as np
from PIL import Image, ImageTk, ImageEnhance, ImageFilter
import pandas as pd
import datetime
import time
import tkinter.font as font
from tkinter import filedialog
import tkinter.messagebox as tm
import CNN as nn
import RNN as rnn
import CNNLSTM as clstm
import PredictCNN as pre
import Segmentation as cd
from tkinter import ttk
import os
import json
import sqlite3
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import seaborn as sns
from threading import Thread
import queue

# Enhanced Theme System
class ThemeManager:
    def __init__(self):
        self.current_theme = "light"
        self.themes = {
            "light": {
                "primary": "#2E86AB",
                "secondary": "#A23B72",
                "accent": "#F18F01",
                "background": "#F5F5F5",
                "surface": "#FFFFFF",
                "text": "#2C3E50",
                "text_secondary": "#7F8C8D",
                "success": "#27AE60",
                "warning": "#F39C12",
                "error": "#E74C3C",
                "hover": "#1E5F7A"
            },
            "dark": {
                "primary": "#3498DB",
                "secondary": "#9B59B6",
                "accent": "#E67E22",
                "background": "#2C3E50",
                "surface": "#34495E",
                "text": "#ECF0F1",
                "text_secondary": "#BDC3C7",
                "success": "#2ECC71",
                "warning": "#F1C40F",
                "error": "#E74C3C",
                "hover": "#2980B9"
            },
            "medical": {
                "primary": "#1ABC9C",
                "secondary": "#3498DB",
                "accent": "#E74C3C",
                "background": "#F8F9FA",
                "surface": "#FFFFFF",
                "text": "#2C3E50",
                "text_secondary": "#7F8C8D",
                "success": "#27AE60",
                "warning": "#F39C12",
                "error": "#E74C3C",
                "hover": "#16A085"
            }
        }

    def get_color(self, color_name):
        return self.themes[self.current_theme][color_name]

    def switch_theme(self, theme_name):
        if theme_name in self.themes:
            self.current_theme = theme_name
            return True
        return False

# Initialize theme manager
theme = ThemeManager()

# Global variables
selected_image_path = ""
preview_image = None
analysis_history = []
current_analysis = None
zoom_factor = 1.0
image_filters = {
    "original": None,
    "enhanced": None,
    "grayscale": None,
    "edge_detect": None
}

# Database setup for analysis history
def init_database():
    conn = sqlite3.connect('malaria_analysis.db')
    cursor = conn.cursor()
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS analysis_history (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            filename TEXT,
            filepath TEXT,
            result TEXT,
            treatment TEXT,
            confidence REAL,
            timestamp DATETIME,
            model_used TEXT
        )
    ''')
    conn.commit()
    conn.close()

# Enhanced Main Application Class
class MalariaDetectionApp:
    def __init__(self):
        self.window = tk.Tk()
        self.window.title("🔬 Advanced Malaria Detection System")
        self.window.geometry('1400x900')
        self.window.configure(background=theme.get_color("background"))
        self.window.resizable(True, True)

        # Center window
        self.center_window()

        # Initialize database
        init_database()

        # Create UI
        self.create_menu()
        self.create_main_interface()

        # Initialize variables
        self.selected_image_path = ""
        self.preview_image = None
        self.zoom_factor = 1.0
        self.analysis_queue = queue.Queue()

    def center_window(self):
        self.window.update_idletasks()
        x = (self.window.winfo_screenwidth() // 2) - (1400 // 2)
        y = (self.window.winfo_screenheight() // 2) - (900 // 2)
        self.window.geometry(f'1400x900+{x}+{y}')

    def create_menu(self):
        """Create enhanced menu bar"""
        menubar = tk.Menu(self.window)
        self.window.config(menu=menubar)

        # File menu
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="File", menu=file_menu)
        file_menu.add_command(label="Open Image", command=self.browse_image, accelerator="Ctrl+O")
        file_menu.add_command(label="Batch Process", command=self.batch_process)
        file_menu.add_separator()
        file_menu.add_command(label="Export Report", command=self.export_report)
        file_menu.add_separator()
        file_menu.add_command(label="Exit", command=self.window.quit)

        # View menu
        view_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="View", menu=view_menu)
        view_menu.add_command(label="Light Theme", command=lambda: self.switch_theme("light"))
        view_menu.add_command(label="Dark Theme", command=lambda: self.switch_theme("dark"))
        view_menu.add_command(label="Medical Theme", command=lambda: self.switch_theme("medical"))
        view_menu.add_separator()
        view_menu.add_command(label="Zoom In", command=self.zoom_in, accelerator="Ctrl++")
        view_menu.add_command(label="Zoom Out", command=self.zoom_out, accelerator="Ctrl+-")
        view_menu.add_command(label="Reset Zoom", command=self.reset_zoom, accelerator="Ctrl+0")

        # Tools menu
        tools_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Tools", menu=tools_menu)
        tools_menu.add_command(label="Image Enhancement", command=self.show_enhancement_tools)
        tools_menu.add_command(label="Model Comparison", command=self.compare_models)
        tools_menu.add_command(label="Statistics Dashboard", command=self.show_statistics)

        # Help menu
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Help", menu=help_menu)
        help_menu.add_command(label="User Guide", command=self.show_help)
        help_menu.add_command(label="About", command=self.show_about)

        # Keyboard bindings
        self.window.bind('<Control-o>', lambda e: self.browse_image())
        self.window.bind('<Control-plus>', lambda e: self.zoom_in())
        self.window.bind('<Control-minus>', lambda e: self.zoom_out())
        self.window.bind('<Control-0>', lambda e: self.reset_zoom())

    def create_main_interface(self):
        """Create the main tabbed interface"""
        # Create notebook for tabs
        self.notebook = ttk.Notebook(self.window)
        self.notebook.pack(fill="both", expand=True, padx=10, pady=10)

        # Create tabs
        self.create_analysis_tab()
        self.create_history_tab()
        self.create_statistics_tab()
        self.create_settings_tab()

    def create_analysis_tab(self):
        """Create the main analysis tab"""
        analysis_frame = Frame(self.notebook, bg=theme.get_color("background"))
        self.notebook.add(analysis_frame, text="🔬 Analysis")

        # Configure grid
        analysis_frame.grid_rowconfigure(0, weight=1)
        analysis_frame.grid_columnconfigure(0, weight=1)
        analysis_frame.grid_columnconfigure(1, weight=1)

        # Left panel - Image processing
        left_panel = Frame(analysis_frame, bg=theme.get_color("surface"), relief="raised", bd=1)
        left_panel.grid(row=0, column=0, sticky="nsew", padx=(10, 5), pady=10)
        left_panel.grid_rowconfigure(2, weight=1)

        # Left panel header
        left_header = Frame(left_panel, bg=theme.get_color("primary"), height=50)
        left_header.grid(row=0, column=0, sticky="ew")
        left_header.grid_propagate(False)
        left_header.grid_columnconfigure(0, weight=1)

        tk.Label(
            left_header,
            text="📁 Image Processing",
            bg=theme.get_color("primary"),
            fg=theme.get_color("surface"),
            font=('Arial', 14, 'bold'),
            pady=12
        ).grid(row=0, column=0)

        # Image controls
        self.create_image_controls(left_panel)

        # Right panel - Results and analysis
        right_panel = Frame(analysis_frame, bg=theme.get_color("surface"), relief="raised", bd=1)
        right_panel.grid(row=0, column=1, sticky="nsew", padx=(5, 10), pady=10)
        right_panel.grid_rowconfigure(2, weight=1)

        # Right panel header
        right_header = Frame(right_panel, bg=theme.get_color("accent"), height=50)
        right_header.grid(row=0, column=0, sticky="ew")
        right_header.grid_propagate(False)
        right_header.grid_columnconfigure(0, weight=1)

        tk.Label(
            right_header,
            text="🔬 Analysis Results",
            bg=theme.get_color("accent"),
            fg=theme.get_color("surface"),
            font=('Arial', 14, 'bold'),
            pady=12
        ).grid(row=0, column=0)

        # Results area
        self.create_results_area(right_panel)

        # Bottom control panel
        self.create_control_panel(analysis_frame)

    def create_image_controls(self, parent):
        """Create image upload and processing controls"""
        controls_frame = Frame(parent, bg=theme.get_color("surface"), padx=20, pady=20)
        controls_frame.grid(row=1, column=0, sticky="ew")
        controls_frame.grid_columnconfigure(1, weight=1)

        # File selection
        tk.Label(
            controls_frame,
            text="Select Blood Cell Image:",
            bg=theme.get_color("surface"),
            fg=theme.get_color("text"),
            font=('Arial', 11, 'bold')
        ).grid(row=0, column=0, columnspan=3, sticky="w", pady=(0, 10))

        # File path entry
        self.file_path_var = tk.StringVar()
        self.file_entry = tk.Entry(
            controls_frame,
            textvariable=self.file_path_var,
            font=('Arial', 10),
            bg=theme.get_color("background"),
            fg=theme.get_color("text"),
            relief="solid",
            bd=1,
            width=35
        )
        self.file_entry.grid(row=1, column=0, columnspan=2, sticky="ew", pady=(0, 15))

        # Browse button
        self.browse_btn = self.create_button(
            controls_frame, "📂 Browse", self.browse_image,
            theme.get_color("primary"), 1, 2, 12
        )

        # Image processing options
        tk.Label(
            controls_frame,
            text="Image Processing:",
            bg=theme.get_color("surface"),
            fg=theme.get_color("text"),
            font=('Arial', 11, 'bold')
        ).grid(row=2, column=0, columnspan=3, sticky="w", pady=(20, 10))

        # Filter options
        self.filter_var = tk.StringVar(value="original")
        filters = [("Original", "original"), ("Enhanced", "enhanced"),
                  ("Grayscale", "grayscale"), ("Edge Detection", "edge_detect")]

        for i, (text, value) in enumerate(filters):
            tk.Radiobutton(
                controls_frame,
                text=text,
                variable=self.filter_var,
                value=value,
                bg=theme.get_color("surface"),
                fg=theme.get_color("text"),
                selectcolor=theme.get_color("background"),
                command=self.apply_filter
            ).grid(row=3+i//2, column=i%2, sticky="w", padx=(0, 10))

        # Image preview area
        self.create_image_preview(parent)

    def create_image_preview(self, parent):
        """Create image preview with zoom and pan"""
        preview_frame = Frame(parent, bg=theme.get_color("surface"), padx=20, pady=10)
        preview_frame.grid(row=2, column=0, sticky="nsew")
        preview_frame.grid_rowconfigure(1, weight=1)
        preview_frame.grid_columnconfigure(0, weight=1)

        tk.Label(
            preview_frame,
            text="Image Preview:",
            bg=theme.get_color("surface"),
            fg=theme.get_color("text"),
            font=('Arial', 11, 'bold')
        ).grid(row=0, column=0, sticky="w", pady=(0, 10))

        # Canvas for image with scrollbars
        canvas_frame = Frame(preview_frame, bg=theme.get_color("background"))
        canvas_frame.grid(row=1, column=0, sticky="nsew")
        canvas_frame.grid_rowconfigure(0, weight=1)
        canvas_frame.grid_columnconfigure(0, weight=1)

        self.image_canvas = Canvas(
            canvas_frame,
            bg=theme.get_color("background"),
            relief="sunken",
            bd=2,
            width=400,
            height=300
        )

        # Scrollbars
        v_scrollbar = Scrollbar(canvas_frame, orient="vertical", command=self.image_canvas.yview)
        h_scrollbar = Scrollbar(canvas_frame, orient="horizontal", command=self.image_canvas.xview)

        self.image_canvas.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)

        self.image_canvas.grid(row=0, column=0, sticky="nsew")
        v_scrollbar.grid(row=0, column=1, sticky="ns")
        h_scrollbar.grid(row=1, column=0, sticky="ew")

        # Bind mouse events for zoom and pan
        self.image_canvas.bind("<Button-1>", self.start_pan)
        self.image_canvas.bind("<B1-Motion>", self.pan_image)
        self.image_canvas.bind("<MouseWheel>", self.zoom_image)

        # Initial placeholder
        self.show_placeholder()

    def create_results_area(self, parent):
        """Create results display area"""
        results_frame = Frame(parent, bg=theme.get_color("surface"), padx=20, pady=20)
        results_frame.grid(row=1, column=0, sticky="ew")
        results_frame.grid_columnconfigure(0, weight=1)

        # Status label
        self.status_label = tk.Label(
            results_frame,
            text="Status: Ready for analysis",
            bg=theme.get_color("surface"),
            fg=theme.get_color("text"),
            font=('Arial', 11, 'bold')
        )
        self.status_label.grid(row=0, column=0, sticky="w", pady=(0, 15))

        # Results notebook for different views
        results_notebook = ttk.Notebook(results_frame)
        results_notebook.grid(row=1, column=0, sticky="ew", pady=(0, 15))

        # Text results tab
        text_frame = Frame(results_notebook, bg=theme.get_color("surface"))
        results_notebook.add(text_frame, text="📄 Report")

        self.results_text = Text(
            text_frame,
            height=15,
            width=50,
            bg=theme.get_color("background"),
            fg=theme.get_color("text"),
            font=('Arial', 10),
            relief="solid",
            bd=1,
            wrap="word",
            state="disabled"
        )
        self.results_text.pack(fill="both", expand=True, padx=10, pady=10)

        # Confidence chart tab
        chart_frame = Frame(results_notebook, bg=theme.get_color("surface"))
        results_notebook.add(chart_frame, text="📊 Confidence")

        # Model comparison tab
        comparison_frame = Frame(results_notebook, bg=theme.get_color("surface"))
        results_notebook.add(comparison_frame, text="🔬 Models")

        # Initialize chart areas
        self.create_confidence_chart(chart_frame)
        self.create_model_comparison(comparison_frame)

    def create_control_panel(self, parent):
        """Create control buttons panel"""
        buttons_frame = Frame(parent, bg=theme.get_color("background"))
        buttons_frame.grid(row=1, column=0, columnspan=2, pady=20)
        buttons_frame.grid_columnconfigure(0, weight=1)
        buttons_frame.grid_columnconfigure(1, weight=1)
        buttons_frame.grid_columnconfigure(2, weight=1)
        buttons_frame.grid_columnconfigure(3, weight=1)

        # Create control buttons
        self.clear_btn = self.create_button(
            buttons_frame, "🗑️ Clear", self.clear_all,
            theme.get_color("secondary"), 0, 0, 15
        )

        self.analyze_btn = self.create_button(
            buttons_frame, "🔍 Analyze", self.analyze_image,
            theme.get_color("success"), 0, 1, 15
        )

        self.compare_btn = self.create_button(
            buttons_frame, "⚖️ Compare Models", self.compare_models,
            theme.get_color("primary"), 0, 2, 18
        )

        self.export_btn = self.create_button(
            buttons_frame, "💾 Export", self.export_report,
            theme.get_color("warning"), 0, 3, 15
        )

    def create_button(self, parent, text, command, bg_color, row, col, width=15):
        """Create a modern styled button"""
        btn = tk.Button(
            parent,
            text=text,
            command=command,
            bg=bg_color,
            fg=theme.get_color("surface"),
            font=('Arial', 10, 'bold'),
            relief="flat",
            bd=0,
            padx=15,
            pady=8,
            cursor="hand2",
            width=width
        )
        btn.grid(row=row, column=col, padx=5, pady=5, sticky="ew")

        # Add hover effects
        def on_enter(e):
            btn.config(bg=theme.get_color("hover"))
        def on_leave(e):
            btn.config(bg=bg_color)

        btn.bind("<Enter>", on_enter)
        btn.bind("<Leave>", on_leave)
        return btn

    # Core functionality methods
    def show_placeholder(self):
        """Show placeholder text in image canvas"""
        self.image_canvas.delete("all")
        self.image_canvas.create_text(
            200, 150,
            text="No image selected\n\nClick 'Browse' to select\na blood cell image\n\nSupported formats: JPG, PNG, BMP, TIFF",
            fill=theme.get_color("text_secondary"),
            font=('Arial', 11),
            justify="center"
        )

    def browse_image(self):
        """Browse and select an image file"""
        file_types = [
            ("Image files", "*.jpg *.jpeg *.png *.bmp *.tiff *.tif"),
            ("All files", "*.*")
        ]

        file_path = filedialog.askopenfilename(
            title="Select Blood Cell Image",
            filetypes=file_types
        )

        if file_path:
            self.selected_image_path = file_path
            self.file_path_var.set(file_path)
            self.load_and_display_image(file_path)

    def load_and_display_image(self, file_path):
        """Load and display image with preview"""
        try:
            # Load original image
            self.original_image = Image.open(file_path)
            self.current_image = self.original_image.copy()

            # Apply current filter
            self.apply_filter()

            # Update status
            self.status_label.config(
                text=f"Status: Image loaded - {os.path.basename(file_path)}",
                fg=theme.get_color("success")
            )

            # Update results
            self.update_results_text(
                f"Image loaded successfully!\n\n"
                f"File: {os.path.basename(file_path)}\n"
                f"Path: {file_path}\n"
                f"Size: {self.original_image.size}\n"
                f"Mode: {self.original_image.mode}\n\n"
                f"Click 'Analyze' to start malaria detection."
            )

        except Exception as e:
            tm.showerror("Error", f"Failed to load image: {str(e)}")
            self.selected_image_path = ""
            self.file_path_var.set("")

    def apply_filter(self):
        """Apply selected image filter"""
        if not hasattr(self, 'original_image'):
            return

        filter_type = self.filter_var.get()

        if filter_type == "original":
            self.current_image = self.original_image.copy()
        elif filter_type == "enhanced":
            enhancer = ImageEnhance.Contrast(self.original_image)
            self.current_image = enhancer.enhance(1.5)
            enhancer = ImageEnhance.Sharpness(self.current_image)
            self.current_image = enhancer.enhance(1.2)
        elif filter_type == "grayscale":
            self.current_image = self.original_image.convert('L').convert('RGB')
        elif filter_type == "edge_detect":
            gray = self.original_image.convert('L')
            edges = gray.filter(ImageFilter.FIND_EDGES)
            self.current_image = edges.convert('RGB')

        self.display_image()

    def display_image(self):
        """Display current image in canvas"""
        if not hasattr(self, 'current_image'):
            return

        # Calculate display size
        canvas_width = self.image_canvas.winfo_width()
        canvas_height = self.image_canvas.winfo_height()

        if canvas_width <= 1 or canvas_height <= 1:
            canvas_width, canvas_height = 400, 300

        # Resize image for display
        img_copy = self.current_image.copy()
        img_copy.thumbnail((int(canvas_width * self.zoom_factor),
                           int(canvas_height * self.zoom_factor)),
                          Image.Resampling.LANCZOS)

        self.preview_image = ImageTk.PhotoImage(img_copy)

        # Clear canvas and display image
        self.image_canvas.delete("all")
        self.image_canvas.create_image(
            canvas_width//2, canvas_height//2,
            image=self.preview_image,
            anchor="center"
        )

        # Update scroll region
        self.image_canvas.configure(scrollregion=self.image_canvas.bbox("all"))

    # Analysis and utility methods
    def analyze_image(self):
        """Perform malaria prediction with enhanced features"""
        if not self.selected_image_path:
            tm.showerror("Error", "Please select an image first!")
            return

        try:
            self.status_label.config(
                text="Status: Analyzing image...",
                fg=theme.get_color("warning")
            )
            self.window.update()

            # Perform prediction
            result, treatment = pre.process(self.selected_image_path)

            # Save to history
            self.save_to_history(result, treatment)

            # Update results display
            result_text = f"""🔬 MALARIA DETECTION RESULTS
{'='*50}

📊 ANALYSIS RESULT: {result}

💊 TREATMENT RECOMMENDATION:
{treatment}

📋 ANALYSIS DETAILS:
• Image: {os.path.basename(self.selected_image_path)}
• Date: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
• Model: AI-based Classification
• Filter Applied: {self.filter_var.get().title()}

⚠️  IMPORTANT DISCLAIMER:
This is an AI analysis tool for research purposes.
Always consult with medical professionals for proper diagnosis.
"""

            self.update_results_text(result_text)

            # Update status
            if "Normal" in str(result):
                self.status_label.config(
                    text="Status: Analysis complete - Normal cells detected",
                    fg=theme.get_color("success")
                )
            else:
                self.status_label.config(
                    text="Status: Analysis complete - Malaria parasites detected",
                    fg=theme.get_color("error")
                )

            # Show popup
            tm.showinfo("Analysis Complete", f"Result: {result}\n\nTreatment: {treatment}")

        except Exception as e:
            error_msg = f"Error during analysis: {str(e)}"
            self.update_results_text(f"❌ ANALYSIS ERROR\n\n{error_msg}\n\nPlease try again with a different image.")
            self.status_label.config(text="Status: Analysis failed", fg=theme.get_color("error"))
            tm.showerror("Analysis Error", error_msg)

    def clear_all(self):
        """Clear all inputs and results"""
        self.selected_image_path = ""
        self.preview_image = None
        self.file_path_var.set("")
        self.filter_var.set("original")

        # Reset preview
        self.show_placeholder()

        # Clear results
        self.update_results_text("Results cleared.\n\nSelect an image to begin analysis.")
        self.status_label.config(text="Status: Ready for analysis", fg=theme.get_color("text"))

    def update_results_text(self, text):
        """Update the results text area"""
        self.results_text.config(state="normal")
        self.results_text.delete(1.0, tk.END)
        self.results_text.insert(tk.END, text)
        self.results_text.config(state="disabled")

    # Zoom and pan methods
    def zoom_in(self):
        """Zoom in on image"""
        self.zoom_factor = min(self.zoom_factor * 1.2, 5.0)
        self.display_image()

    def zoom_out(self):
        """Zoom out on image"""
        self.zoom_factor = max(self.zoom_factor / 1.2, 0.1)
        self.display_image()

    def reset_zoom(self):
        """Reset zoom to original"""
        self.zoom_factor = 1.0
        self.display_image()

    def start_pan(self, event):
        """Start panning operation"""
        self.image_canvas.scan_mark(event.x, event.y)

    def pan_image(self, event):
        """Pan the image"""
        self.image_canvas.scan_dragto(event.x, event.y, gain=1)

    def zoom_image(self, event):
        """Zoom with mouse wheel"""
        if event.delta > 0:
            self.zoom_in()
        else:
            self.zoom_out()

    # Placeholder methods for additional features
    def create_history_tab(self):
        """Create analysis history tab"""
        history_frame = Frame(self.notebook, bg=theme.get_color("background"))
        self.notebook.add(history_frame, text="📚 History")

        tk.Label(
            history_frame,
            text="Analysis History\n\nFeature coming soon...",
            bg=theme.get_color("background"),
            fg=theme.get_color("text"),
            font=('Arial', 14)
        ).pack(expand=True)

    def create_statistics_tab(self):
        """Create statistics dashboard tab"""
        stats_frame = Frame(self.notebook, bg=theme.get_color("background"))
        self.notebook.add(stats_frame, text="📊 Statistics")

        tk.Label(
            stats_frame,
            text="Statistics Dashboard\n\nFeature coming soon...",
            bg=theme.get_color("background"),
            fg=theme.get_color("text"),
            font=('Arial', 14)
        ).pack(expand=True)

    def create_settings_tab(self):
        """Create settings tab"""
        settings_frame = Frame(self.notebook, bg=theme.get_color("background"))
        self.notebook.add(settings_frame, text="⚙️ Settings")

        tk.Label(
            settings_frame,
            text="Application Settings\n\nFeature coming soon...",
            bg=theme.get_color("background"),
            fg=theme.get_color("text"),
            font=('Arial', 14)
        ).pack(expand=True)

    def create_confidence_chart(self, parent):
        """Create confidence chart placeholder"""
        tk.Label(
            parent,
            text="Confidence Chart\n\nWill show prediction confidence levels",
            bg=theme.get_color("surface"),
            fg=theme.get_color("text"),
            font=('Arial', 12)
        ).pack(expand=True)

    def create_model_comparison(self, parent):
        """Create model comparison placeholder"""
        tk.Label(
            parent,
            text="Model Comparison\n\nWill compare CNN, RNN, and CNN-LSTM results",
            bg=theme.get_color("surface"),
            fg=theme.get_color("text"),
            font=('Arial', 12)
        ).pack(expand=True)

    # Utility methods
    def save_to_history(self, result, treatment):
        """Save analysis to database"""
        try:
            conn = sqlite3.connect('malaria_analysis.db')
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO analysis_history
                (filename, filepath, result, treatment, timestamp, model_used)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (
                os.path.basename(self.selected_image_path),
                self.selected_image_path,
                str(result),
                str(treatment),
                datetime.datetime.now(),
                "CNN-LSTM"
            ))
            conn.commit()
            conn.close()
        except Exception as e:
            print(f"Error saving to history: {e}")

    def switch_theme(self, theme_name):
        """Switch application theme"""
        if theme.switch_theme(theme_name):
            tm.showinfo("Theme Changed", f"Theme switched to {theme_name.title()}")
            # Note: Full theme switching would require recreating widgets

    # Placeholder methods for menu functions
    def batch_process(self):
        tm.showinfo("Feature", "Batch processing feature coming soon!")

    def export_report(self):
        tm.showinfo("Feature", "Export report feature coming soon!")

    def show_enhancement_tools(self):
        tm.showinfo("Feature", "Image enhancement tools coming soon!")

    def compare_models(self):
        tm.showinfo("Feature", "Model comparison feature coming soon!")

    def show_statistics(self):
        tm.showinfo("Feature", "Statistics dashboard coming soon!")

    def show_help(self):
        tm.showinfo("Help", "Malaria Detection System\n\nSelect an image and click Analyze to detect malaria parasites.")

    def show_about(self):
        tm.showinfo("About", "Advanced Malaria Detection System\nVersion 2.0\n\nPowered by AI and Machine Learning")

    def run(self):
        """Start the application"""
        # Initialize with welcome message
        self.update_results_text(
            "🔬 Welcome to Advanced Malaria Detection System!\n\n"
            "This enhanced AI-powered tool analyzes blood cell images to detect malaria parasites.\n\n"
            "✨ NEW FEATURES:\n"
            "• Multiple themes (Light, Dark, Medical)\n"
            "• Image filters and enhancement\n"
            "• Zoom and pan functionality\n"
            "• Analysis history tracking\n"
            "• Model comparison tools\n\n"
            "🚀 TO GET STARTED:\n"
            "1. Click 'Browse' to select a blood cell image\n"
            "2. Choose image filters if desired\n"
            "3. Click 'Analyze' to start detection\n"
            "4. View detailed results and recommendations\n\n"
            "📁 Supported formats: JPG, PNG, BMP, TIFF"
        )

        self.window.mainloop()

# Create and run the application
if __name__ == "__main__":
    app = MalariaDetectionApp()
    app.run()
